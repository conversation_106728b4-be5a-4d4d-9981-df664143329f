import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
from openpyxl import load_workbook
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import dashscope
from dashscope import Generation
import os
import logging
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', filename='translation.log', filemode='w')

def strip_punct(word):
    # 对于撇号组合词，提取基础词用于词表查找
    # 如 "film's" -> "film", "Newton's" -> "newton"
    if "'" in word and len(word) > 2:
        # 提取撇号前的基础词
        base_word = word.split("'")[0]
        return re.sub(r'[\s\.,!?;:，。!""''\(\)\'"—…；：？！\[\]{}]+$', '', base_word).lower()
    else:
        # 普通单词的处理
        return re.sub(r'[\s\.,!?;:，。!“”‘’\(\)\'"—…；：？！\[\]{}]+$', '', word).lower()

def split_meaning_two_lines(meaning, max_len=8):
    seps = ['，', ',', '。', '；', ';', '、', ' ']
    for s in seps:
        if s in meaning and len(meaning) > max_len:
            idx = meaning.find(s)
            if idx > 0 and idx < len(meaning)-1:
                return meaning[:idx+1] + '\n' + meaning[idx+1:]
    if len(meaning) > max_len:
        mid = len(meaning) // 2
        return meaning[:mid] + '\n' + meaning[mid:]
    return meaning

def set_table_noborder_and_tight(table, colwidths):
    tbl = table._tbl
    tblPr = tbl.xpath('./w:tblPr')[0]
    tblBorders = OxmlElement('w:tblBorders')
    for border in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
        border_elem = OxmlElement(f'w:{border}')
        border_elem.set(qn('w:val'), 'none')
        tblBorders.append(border_elem)
    tblPr.append(tblBorders)

    for cell in table._cells:
        tcp = cell._tc.get_or_add_tcPr()
        tcMar = OxmlElement('w:tcMar')
        for m in ('top', 'left', 'bottom', 'right'):
            node = OxmlElement('w:' + m)
            node.set(qn('w:w'), "0")
            node.set(qn('w:type'), "dxa")
            tcMar.append(node)
        tcp.append(tcMar)

    for row in table.rows:
        for idx, cell in enumerate(row.cells):
            if idx < len(colwidths):
                cell.width = colwidths[idx]

def load_wordlist(excel_path):
    wb = load_workbook(excel_path)
    ws = wb.active
    word_dict = {}
    word_set = set()
    header = [str(i).strip() for i in next(ws.iter_rows(values_only=True))]
    word_idx = 0
    mean_idx = 1
    if "单词" in header:
        word_idx = header.index("单词")
    if "释义" in header:
        mean_idx = header.index("释义")
    for row in ws.iter_rows(min_row=2, values_only=True):
        if row and row[word_idx]:
            w = str(row[word_idx]).strip()
            w_norm = re.sub(r'[^A-Za-z0-9\'\-]', '', w).lower()
            word_set.add(w_norm)
            mean = str(row[mean_idx]).strip() if len(row) > mean_idx and row[mean_idx] else ""
            word_dict[w_norm] = mean
    return word_set, word_dict

def extract_tokens(text):
    # 预处理：去掉km和h之间的多余空格
    text = re.sub(r'km\s+/\s+h', 'km/h', text)

    # 处理特殊字符间的空格问题
    # 处理数字+特定符号的情况，如"1000 x" -> "1000x"（只限于特定符号）
    # 包括：x,y,z等数学变量，上标数字，百分号，度数符号，π等
    text = re.sub(r'(\d+)\s+([xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°])', r'\1\2', text)

    # 全面的撇号处理，支持所有撇号字符和复杂空白情况
    # 定义所有可能的撇号字符：'、'、'、`、'、'等（包括Unicode字符）
    apostrophes = r"['\'`\u2018\u2019\u00B4]"

    # 1. 处理单词+'s的情况（如 "film ' s" -> "film's"）
    # 支持任意空白字符（空格、制表符等）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*s\b", r"\1's", text)

    # 2. 处理单词+'t的情况（如 "don ' t" -> "don't"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*t\b", r"\1't", text)

    # 3. 处理单词+'re的情况（如 "they ' re" -> "they're"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*re\b", r"\1're", text)

    # 4. 处理单词+'ll的情况（如 "I ' ll" -> "I'll"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*ll\b", r"\1'll", text)

    # 5. 处理单词+'ve的情况（如 "I ' ve" -> "I've"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*ve\b", r"\1've", text)

    # 6. 处理单词+'d的情况（如 "I ' d" -> "I'd"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*d\b", r"\1'd", text)

    # 7. 处理单词+'m的情况（如 "I ' m" -> "I'm"）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*m\b", r"\1'm", text)

    # 8. 处理其他撇号+字母的情况（通用规则）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s*([a-zA-Z]+)", r"\1'\2", text)

    # 9. 处理孤立的撇号（清理多余空格）
    text = re.sub(rf"(\w+)\s*{apostrophes}\s+", r"\1' ", text)

    # 改进的分词模式，更好地处理语义关联的字符序列
    # 使用更精确的正则表达式来匹配各种模式

    # 改进的基本分词模式，确保撇号组合词不被分割
    # 1. 撇号组合词（如 word's, don't, I'll 等）
    # 2. 连字符单词
    # 3. 数字+上下标组合（如 5², H₂O, x³）
    # 4. 普通单词+上下标组合（如 H₂, CO₂）
    # 5. 普通单词
    # 6. 数字（包括小数）
    # 7. 单个字符（标点符号等）
    basic_pattern = r"""
        [A-Za-z]+['\'`\u2018\u2019\u00B4][a-zA-Z]+ |  # 撇号组合词（支持所有撇号字符，包括Unicode）
        [A-Za-z]+(?:-[A-Za-z]+)+ |        # 连字符连接的单词（如eco-system, double-action）
        \d+(?:\.\d+)?-[A-Za-z]+ |         # 数字+连字符+单词（如5-star, 4.5-point）
        \d+(?:\.\d+)?[²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉]+ |  # 数字+上下标（如5², 10³）
        [A-Za-z]+[²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉]+ |     # 字母+上下标（如H₂, CO₂, x³）
        [A-Za-z]+ |                       # 普通单词
        \d+(?:\.\d+)? |                   # 数字（包括小数）
        [^\w\s]                           # 标点符号和其他字符
    """
    tokens = re.findall(basic_pattern, text, re.VERBOSE)

    # 智能合并：将语义相关的token合并
    result = []
    i = 0
    while i < len(tokens):
        if i >= len(tokens):
            break

        current_token = tokens[i]
        merged_token = current_token
        j = i + 1

        # 持续检查后续token是否应该合并
        while j < len(tokens):
            next_token = tokens[j]
            should_merge = False

            # 1. 货币符号与数字合并（$, €, £, ¥ + 数字）
            if (re.match(r'^[$€£¥]$', merged_token) and
                re.match(r'^\d+', next_token)):
                should_merge = True

            # 2. 数字与特定符号合并（数字 + x, ², π等，但不包括普通英文单词）
            elif (re.match(r'^\d+(\.\d+)?$', merged_token) and
                  re.match(r'^[xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉%π°/]$', next_token)):
                should_merge = True

            # 2a. 数字+字母组合继续与标点合并（如 1000x + .）
            elif (re.match(r'^\d+(\.\d+)?[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉xX%π°/]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—）\)]$', next_token)):
                should_merge = True

            # 3. 数字与单位合并（如 60 + km, km + / + h）
            elif (re.match(r'^\d+$', merged_token) and next_token == 'km'):
                should_merge = True
            elif (merged_token == 'km' and next_token == '/'):
                should_merge = True
            elif (merged_token == 'km/' and next_token == 'h'):
                should_merge = True
            elif (re.match(r'^\d+km$', merged_token) and next_token == '/'):
                should_merge = True
            elif (re.match(r'^\d+km/$', merged_token) and next_token == 'h'):
                should_merge = True

            # 4. 字母与上标/下标合并（包括更多的上下标字符）
            elif (re.match(r'^[a-zA-Z]+$', merged_token) and
                  re.match(r'^[²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉]', next_token)):
                should_merge = True

            # 4a. 数字与上标/下标合并（确保数字+上下标能够合并）
            elif (re.match(r'^\d+(\.\d+)?$', merged_token) and
                  re.match(r'^[²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉]$', next_token)):
                should_merge = True

            # 5. 单词与标点符号合并（word + .!?等，包括连字符单词）
            elif (re.match(r'^[a-zA-Z0-9\-]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—）\)]$', next_token)):
                should_merge = True

            # 6. 已经包含标点的单词继续与标点合并（如 word! + "）
            elif (re.match(r'^[a-zA-Z0-9]+[,\.!?;:\"\"''…—）\)]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', next_token)):
                should_merge = True

            # 7. 标点符号之间的合并（如 ! + "）
            elif (re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', next_token)):
                should_merge = True

            # 7. 数学运算符与数字合并（= + 数字）
            elif (merged_token in ['=', '+', '-', '×', '÷', '/', '*'] and
                  re.match(r'^[\d\w]', next_token)):
                should_merge = True

            # 7a. 数学表达式继续合并（如 =3.14 + π）
            elif (re.match(r'^[=+\-×÷/*]\d+(\.\d+)?$', merged_token) and
                  re.match(r'^[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹π°]', next_token)):
                should_merge = True

            # 8. 小数点合并（数字 + . + 数字）
            elif (re.match(r'^\d+$', merged_token) and next_token == '.' and
                  j + 1 < len(tokens) and re.match(r'^\d+', tokens[j + 1])):
                # 特殊处理：合并三个token（数字.数字）
                if j + 1 < len(tokens):
                    merged_token = merged_token + next_token + tokens[j + 1]
                    j += 2
                    continue

            if should_merge:
                merged_token += next_token
                j += 1
            else:
                break

        result.append(merged_token)
        i = j

    return result

def split_sentence_for_context(text, idx):
    s, e = 0, len(text)
    for i in range(idx, 0, -1):
        if text[i] in '.!?':
            s = i + 1
            break
    for i in range(idx, len(text)):
        if text[i] in '.!?':
            e = i + 1
            break
    return text[s:e].strip()

def get_chinese_meaning_batch(api_key, tasks, progress_cb=lambda x, y: None):
    dashscope.api_key = api_key
    result = {}
    with ThreadPoolExecutor(max_workers=8) as pool:
        future_to_key = {}
        for (word, sent) in tasks:
            future = pool.submit(get_chinese_meaning, api_key, word, sent)
            future_to_key[future] = (word, sent)
        total = len(future_to_key)
        for idx, f in enumerate(as_completed(future_to_key)):
            key = future_to_key[f]
            try:
                val = f.result()
            except Exception:
                val = ""
            result[key] = val
            percent = int((idx + 1) / total * 100)
            progress_cb(f"AI进度：{idx + 1}/{total}", percent)
    return result

def get_chinese_meaning(api_key, word, sentence):
    prompt = f"请给出英文句子：'{sentence}' 中单词 '{word}' 在该句中的最准确中文意思，不要泛泛释义。直接输出汉字即可。"
    logging.info(f"Requesting translation for '{word}' with prompt: {prompt}")
    messages = [
        {"role": "system", "content": "你是地道的英汉词义专家"},
        {"role": "user", "content": prompt}
    ]
    try:
        response = Generation.call(
            model="qwen-turbo",
            messages=messages,
            result_format="message",
            max_tokens=32
        )
        translation = response.output.choices[0].message.content.strip()
        logging.info(f"API Response for '{word}': {response}")
        logging.info(f"Received translation for '{word}': '{translation}'")
        return translation
    except Exception as e:
        logging.error(f"Error translating '{word}': {e}", exc_info=True)
        return ""

def split_sentence_by_period(text):
    sentences = text.split(".")
    return [sentence.strip() + '.' for sentence in sentences if sentence.strip()]

class WordAssistantApp:
    def __init__(self, root):
        self.root = root
        self.root.title("智能词义文档处理器（自适应单元格宽度）")
        self.config_file = "app_config.json"
        style = ttk.Style()
        style.theme_use('clam')

        file_frame = ttk.LabelFrame(root, text="文件选择", padding=10)
        file_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        ttk.Label(file_frame, text="Word文档A:").grid(row=0, column=0, sticky="e")
        self.word_path = ttk.Entry(file_frame, width=50)
        self.word_path.grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="选择", command=self.select_word).grid(row=0, column=2, padx=5)
        ttk.Label(file_frame, text="选择要处理的Word文档").grid(row=0, column=3, padx=5, sticky="w")

        ttk.Label(file_frame, text="Excel词表B:").grid(row=1, column=0, sticky="e")
        self.excel_path = ttk.Entry(file_frame, width=50)
        self.excel_path.grid(row=1, column=1, padx=5)
        ttk.Button(file_frame, text="选择", command=self.select_excel).grid(row=1, column=2, padx=5)
        ttk.Label(file_frame, text="选择包含词表的Excel文件").grid(row=1, column=3, padx=5, sticky="w")

        settings_frame = ttk.LabelFrame(root, text="设置", padding=10)
        settings_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        ttk.Label(settings_frame, text="中文字体:").grid(row=0, column=0, sticky="e")
        self.fonttype = tk.StringVar(value="宋体")
        ttk.OptionMenu(settings_frame, self.fonttype, "宋体", "黑体", "楷体", "微软雅黑").grid(row=0, column=1, sticky="w")
        ttk.Label(settings_frame, text="中文释义的字体").grid(row=0, column=2, padx=5, sticky="w")

        ttk.Label(settings_frame, text="中文字号:").grid(row=1, column=0, sticky="e")
        self.fontsize = tk.IntVar(value=10)
        ttk.OptionMenu(settings_frame, self.fontsize, 8, 9, 10, 12, 14).grid(row=1, column=1, sticky="w")
        ttk.Label(settings_frame, text="中文释义的字号").grid(row=1, column=2, padx=5, sticky="w")

        ttk.Label(settings_frame, text="API Key:").grid(row=2, column=0, sticky="e")
        self.apikey = ttk.Entry(settings_frame, width=50)
        self.apikey.grid(row=2, column=1)
        ttk.Label(settings_frame, text="用于AI翻译的API密钥").grid(row=2, column=2, padx=5, sticky="w")

        # 加载保存的API Key
        self.load_config()

        ttk.Label(settings_frame, text="最大单元格宽度(厘米):").grid(row=3, column=0, sticky="e")
        self.max_width_var = tk.DoubleVar(value=2.2)
        ttk.Spinbox(settings_frame, from_=1.0, to=5.0, increment=0.01, width=7, textvariable=self.max_width_var).grid(row=3, column=1, sticky='w')
        ttk.Label(settings_frame, text="表格单元格的最大宽度").grid(row=3, column=2, padx=5, sticky="w")

        self.progress = ttk.Label(root, text="")
        self.progress.grid(row=2, column=0, pady=5)
        self.progressbar = ttk.Progressbar(root, orient="horizontal", length=340, mode="determinate", maximum=100)
        self.progressbar.grid(row=3, column=0, pady=10)

        button_frame = ttk.Frame(root)
        button_frame.grid(row=4, column=0, pady=10)
        ttk.Button(button_frame, text="处理文档A", command=self.process_document_A).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="帮助", command=self.show_help).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="开始处理", command=self.run).grid(row=0, column=2, padx=5)

        # 绑定窗口关闭事件，保存配置
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 加载API Key
                    if 'api_key' in config:
                        self.apikey.delete(0, tk.END)
                        self.apikey.insert(0, config['api_key'])
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'api_key': self.apikey.get().strip()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def on_closing(self):
        """窗口关闭时的处理"""
        self.save_config()
        self.root.destroy()

    def select_word(self):
        p = filedialog.askopenfilename(filetypes=[("Word文档", "*.docx")])
        if p:
            self.word_path.delete(0, tk.END)
            self.word_path.insert(0, p)

    def select_excel(self):
        p = filedialog.askopenfilename(filetypes=[("Excel", "*.xlsx")])
        if p:
            self.excel_path.delete(0, tk.END)
            self.excel_path.insert(0, p)

    def set_progress(self, txt, percent=None):
        self.progress.config(text=txt)
        if percent is not None:
            self.progressbar['value'] = percent
        self.root.update()

    def run(self):
        wordf = self.word_path.get()
        excelf = self.excel_path.get()
        apikey = self.apikey.get().strip()
        if not wordf:
            messagebox.showerror("错误", "请选择Word文档！")
            return
        if not excelf:
            messagebox.showerror("错误", "请选择Excel词表！")
            return
        if not apikey:
            messagebox.showerror("错误", "请填写API Key！")
            return
        self.set_progress("开始处理", 0)

        def _worker():
            try:
                outpath = self.process_doc(wordf, excelf, self.fonttype.get(), self.fontsize.get(), apikey, self.set_progress)
                self.root.after(0, lambda: messagebox.showinfo("完成", f"处理完成！输出文档为：\n{outpath}"))
            except Exception as e:
                err_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败：{err_msg}"))
            self.set_progress("", 0)

        threading.Thread(target=_worker, daemon=True).start()

    def show_help(self):
        help_text = (
            "使用说明：\n"
            "1. Word文档A：选择需要处理的Word文档。\n"
            "2. Excel词表B：选择包含单词和释义的Excel文件。\n"
            "3. 中文字体和字号：设置中文释义的字体和大小。\n"
            "4. API Key：输入用于AI翻译的密钥。\n"
            "5. 最大单元格宽度：设置表格单元格的最大宽度（单位：厘米）。\n"
            "6. 点击“开始处理”生成带释义的文档。"
        )
        messagebox.showinfo("帮助", help_text)

    def process_document_A(self):
        wordf = self.word_path.get()
        if not wordf:
            messagebox.showerror("错误", "请选择Word文档！")
            return
        
        doc_in = Document(wordf)
        doc_out = Document()

        # 复制原文档的纸张大小设置
        if doc_in.sections:
            section_in = doc_in.sections[0]
            section_out = doc_out.sections[0]

            # 复制页面尺寸
            section_out.page_width = section_in.page_width
            section_out.page_height = section_in.page_height

            # 复制页边距
            section_out.left_margin = section_in.left_margin
            section_out.right_margin = section_in.right_margin
            section_out.top_margin = section_in.top_margin
            section_out.bottom_margin = section_in.bottom_margin

            # 复制页面方向
            section_out.orientation = section_in.orientation

        for para in doc_in.paragraphs:
            text = para.text
            sentences = split_sentence_by_period(text)
            
            for sentence in sentences:
                if sentence.strip():
                    font_name = None
                    font_size = None
                    start_pos = text.find(sentence)
                    if start_pos != -1 and para.runs:
                        current_pos = 0
                        for run in para.runs:
                            run_text = run.text
                            run_len = len(run_text)
                            if current_pos <= start_pos < current_pos + run_len:
                                font_name = run.font.name if run.font.name else "Georgia"
                                font_size = run.font.size.pt if run.font.size else 10
                                break
                            current_pos += run_len
                    
                    if not font_name:
                        font_name = "Georgia"
                    if not font_size:
                        font_size = 10
                    
                    para_out = doc_out.add_paragraph()
                    run = para_out.add_run(sentence)
                    run.font.name = font_name
                    run.font.size = Pt(font_size)
                    para_out.paragraph_format.space_before = Pt(0)
                    para_out.paragraph_format.space_after = Pt(0)

        output_path = os.path.abspath("processed_output.docx")
        doc_out.save(output_path)

        # 显示完成对话框
        messagebox.showinfo("完成", f"文档处理完成！输出文档为：\n{output_path}")

        # 对话框关闭后，自动更新Word文档A的路径为输出文档路径
        self.word_path.delete(0, tk.END)
        self.word_path.insert(0, output_path)

    def find_word_position_in_text(self, word, text, start_ptr):
        """
        更精确地在文本中定位单词位置，考虑上标下标等特殊字符
        """
        # 首先尝试简单的查找
        pos = text.find(word, start_ptr)
        if pos != -1:
            return pos

        # 如果简单查找失败，尝试更复杂的匹配
        # 处理可能包含上标下标的情况
        import unicodedata

        # 标准化文本，将上标下标转换为普通字符进行匹配
        normalized_text = unicodedata.normalize('NFKD', text[start_ptr:])
        normalized_word = unicodedata.normalize('NFKD', word)

        pos = normalized_text.find(normalized_word)
        if pos != -1:
            return start_ptr + pos

        return -1

    def get_format_at_position(self, para, position, word):
        """
        获取指定位置的格式信息，包括字体、上标、下标等
        特别处理包含上下标字符的单词（如5²、H₂O等）
        """
        format_info = {
            'font_name': "Georgia",
            'font_size': 12,
            'is_superscript': False,
            'is_subscript': False
        }

        if not para.runs:
            return format_info

        # 检查单词是否包含上下标字符
        superscript_chars = '²³¹⁰⁴⁵⁶⁷⁸⁹'
        subscript_chars = '₀₁₂₃₄₅₆₇₈₉'
        has_superscript = any(char in word for char in superscript_chars)
        has_subscript = any(char in word for char in subscript_chars)

        current_pos = 0
        found_main_format = False

        for run in para.runs:
            run_text = run.text
            run_len = len(run_text)

            # 检查位置是否在当前run范围内
            if current_pos <= position < current_pos + run_len:
                format_info['font_name'] = run.font.name if run.font.name else "Georgia"
                format_info['font_size'] = run.font.size.pt if run.font.size else 12

                # 如果当前run有上标或下标格式，记录下来
                if bool(run.font.superscript):
                    format_info['is_superscript'] = True
                if bool(run.font.subscript):
                    format_info['is_subscript'] = True

                found_main_format = True
                break

            # 特殊处理：如果单词跨越多个run，检查所有相关run的格式
            word_end_pos = position + len(word)
            if current_pos < word_end_pos and position < current_pos + run_len:
                # 这个run与我们的单词有重叠
                if not found_main_format:
                    format_info['font_name'] = run.font.name if run.font.name else "Georgia"
                    format_info['font_size'] = run.font.size.pt if run.font.size else 12
                    found_main_format = True

                # 检查这个run是否有上标或下标格式
                if bool(run.font.superscript):
                    format_info['is_superscript'] = True
                if bool(run.font.subscript):
                    format_info['is_subscript'] = True

            current_pos += run_len

        # 如果单词包含上下标字符，但没有检测到相应的格式，
        # 说明这些字符本身就是Unicode上下标字符，应该标记相应的格式
        if has_superscript and not format_info['is_superscript']:
            format_info['is_superscript'] = True
        if has_subscript and not format_info['is_subscript']:
            format_info['is_subscript'] = True

        return format_info

    def _add_formatted_word_to_cell(self, cell, word, font_name, font_size, is_superscript, is_subscript, wordset):
        """
        智能添加格式化单词到单元格，正确处理包含上下标字符的单词
        """
        import re

        # 定义上下标字符
        superscript_chars = '²³¹⁰⁴⁵⁶⁷⁸⁹'
        subscript_chars = '₀₁₂₃₄₅₆₇₈₉'

        # 检查单词是否包含上下标字符
        has_superscript_chars = any(char in word for char in superscript_chars)
        has_subscript_chars = any(char in word for char in subscript_chars)

        if has_superscript_chars or has_subscript_chars:
            # 分解单词为普通部分和上下标部分
            parts = []
            current_part = ""
            current_is_super = False
            current_is_sub = False

            for char in word:
                if char in superscript_chars:
                    # 如果当前部分不为空且格式不同，保存当前部分
                    if current_part and not current_is_super:
                        parts.append((current_part, current_is_super, current_is_sub))
                        current_part = ""
                    current_part += char
                    current_is_super = True
                    current_is_sub = False
                elif char in subscript_chars:
                    # 如果当前部分不为空且格式不同，保存当前部分
                    if current_part and not current_is_sub:
                        parts.append((current_part, current_is_super, current_is_sub))
                        current_part = ""
                    current_part += char
                    current_is_super = False
                    current_is_sub = True
                else:
                    # 普通字符
                    if current_part and (current_is_super or current_is_sub):
                        parts.append((current_part, current_is_super, current_is_sub))
                        current_part = ""
                        current_is_super = False
                        current_is_sub = False
                    current_part += char

            # 添加最后一部分
            if current_part:
                parts.append((current_part, current_is_super, current_is_sub))

            # 为每个部分创建单独的run
            for part_text, part_is_super, part_is_sub in parts:
                run = cell.paragraphs[0].add_run(part_text)
                run.font.name = font_name
                run.font.size = Pt(font_size)
                run.font.superscript = part_is_super
                run.font.subscript = part_is_sub
                run.font.underline = True if strip_punct(word) in wordset else False
        else:
            # 普通单词，直接添加
            run = cell.paragraphs[0].add_run(word)
            run.font.name = font_name
            run.font.size = Pt(font_size)
            run.font.superscript = is_superscript
            run.font.subscript = is_subscript
            run.font.underline = True if strip_punct(word) in wordset else False

    def get_col_widths(self, tokens):
        base_width = 0.32
        char_width = 0.21
        max_width = self.max_width_var.get()
        punc_extra = {
            '.': 0.07, ',': 0.07, '?': 0.09, '!': 0.09, ';': 0.07, ':': 0.07, '-': 0.05,
            '"': 0.11, '“': 0.12, '”': 0.12, '’': 0.055, "'": 0.05,
            '(': 0.05, ')': 0.05, '[': 0.05, ']': 0.05, '…': 0.18,
        }

        # 保持原有的基础参数，不做全局调整
        # 只对连字符单词进行个别优化
        colwidths = []
        for word in tokens:
            # 改进的宽度计算，特别处理连字符单词
            content = re.sub(r"[^A-Za-z0-9\-]", "", word)  # 保留连字符
            width = base_width + len(content) * char_width

            # 对于连字符单词，给予精准的额外宽度
            if '-' in word and len(word) > 5:  # 只处理长度大于5的连字符单词
                # 计算连字符单词需要的最小宽度
                hyphen_count = word.count('-')

                # 精准计算连字符单词的实际显示宽度需求
                estimated_display_width = len(word) * 0.18 + hyphen_count * 0.15 + 1.0

                # 只有当计算宽度小于估算显示宽度时才增加
                if width < estimated_display_width:
                    # 适度的宽度增加，确保不换行
                    additional_width = min(estimated_display_width - width, 2.0)
                    width += additional_width

                # 特别处理一些已知会换行的连字符单词
                problematic_words = ['double-action', 'state-of-the-art', 'user-friendly', 'well-known', 'real-time', 'long-term']
                if word.lower() in problematic_words:
                    # 为这些特定单词设置更大的最小宽度
                    min_width = 3.8
                    if width < min_width:
                        width = min_width

                # 为所有连字符单词设置基础最小宽度
                base_min_width = 2.8
                if width < base_min_width:
                    width = base_min_width

            if word and not word[-1].isalnum():
                width += punc_extra.get(word[-1], 0)

            # 对于连字符单词，适度放宽最大宽度限制
            if '-' in word and len(word) > 8:
                # 对长连字符单词放宽限制，确保不换行
                effective_max_width = min(max_width * 1.8, 4.2)  # 最多放宽到4.2cm
            elif '-' in word and len(word) > 5:
                # 对中等长度连字符单词也适度放宽
                effective_max_width = min(max_width * 1.6, 3.8)  # 最多放宽到3.8cm
            else:
                effective_max_width = max_width

            width = min(width, effective_max_width)
            colwidths.append(Cm(width))
        return colwidths

    def get_font_name(self, font_type):
        """将中文字体名称映射到 Word 支持的英文名称"""
        font_map = {
            "宋体": "SimSun",
            "黑体": "SimHei",
            "楷体": "KaiTi",  # 标准楷体字体名称
            "微软雅黑": "Microsoft YaHei"
        }
        return font_map.get(font_type, "SimSun")  # 默认使用 SimSun

    def set_chinese_font(self, run, font_name, font_size):
        """
        设置中文字体的完整方法，确保字体正确应用
        特别处理楷体等中文字体的兼容性问题
        """
        # 设置基本字体属性
        run.font.name = font_name
        run.font.size = Pt(font_size)

        # 获取run的XML元素
        r = run._r
        rFonts = r.rPr.rFonts

        # 为楷体提供多种字体名称选项，提高兼容性
        if font_name == "KaiTi":
            # 尝试多种楷体字体名称，确保在不同系统上都能正确显示
            font_options = ["KaiTi", "楷体", "STKaiti", "Kai"]
            for font_option in font_options:
                try:
                    rFonts.set(qn('w:ascii'), font_option)
                    rFonts.set(qn('w:hAnsi'), font_option)
                    rFonts.set(qn('w:eastAsia'), font_option)
                    rFonts.set(qn('w:cs'), font_option)
                    break
                except:
                    continue
        else:
            # 其他字体的标准设置
            rFonts.set(qn('w:ascii'), font_name)
            rFonts.set(qn('w:hAnsi'), font_name)
            rFonts.set(qn('w:eastAsia'), font_name)
            rFonts.set(qn('w:cs'), font_name)

    def process_doc(self, input_docx, excel_path, font_name, font_size, api_key, set_progress):
        ch_font_name = self.get_font_name(self.fonttype.get())  # 获取映射后的字体名称
        ch_font_size = self.fontsize.get()
        wordset, worddict = load_wordlist(excel_path)
        doc_in = Document(input_docx)
        doc_out = Document()

        # 复制原文档的纸张大小设置
        if doc_in.sections:
            section_in = doc_in.sections[0]
            section_out = doc_out.sections[0]

            # 复制页面尺寸
            section_out.page_width = section_in.page_width
            section_out.page_height = section_in.page_height

            # 复制页边距
            section_out.left_margin = section_in.left_margin
            section_out.right_margin = section_in.right_margin
            section_out.top_margin = section_in.top_margin
            section_out.bottom_margin = section_in.bottom_margin

            # 复制页面方向
            section_out.orientation = section_in.orientation

        # Define a custom style for minimal spacing
        style_name = 'TinySpacing'
        styles = doc_out.styles
        if style_name not in styles:
            style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
            style.base_style = styles['Normal']
            font = style.font
            font.size = Pt(1)
            pf = style.paragraph_format
            pf.space_before = Pt(0)
            pf.space_after = Pt(0)
            pf.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            pf.line_spacing = Pt(1)
            
        all_tasks = set()
        all_para_tokens = []

        for para in doc_in.paragraphs:
            text = para.text
            tokens = extract_tokens(text)
            all_para_tokens.append((tokens, text, para))  # 保存 para 对象以获取字体信息
            ptr = 0
            for token in tokens:
                tkey = strip_punct(token)
                idx = text.lower().find(tkey, ptr) if tkey else -1
                ptr = idx + len(tkey) if idx != -1 else ptr
                if tkey and tkey in wordset and idx != -1:
                    sent = split_sentence_for_context(text, idx).strip()
                    key = (tkey, sent)
                    all_tasks.add(key)

        all_tasks = list(all_tasks)
        set_progress(f"共需AI释义：{len(all_tasks)}", 0)

        def progress_cb(txt, percent=None):
            set_progress(txt, percent)

        meaning_cache = get_chinese_meaning_batch(api_key, all_tasks, progress_cb)
        set_progress("AI查完，排版生成Word...", 100)

        for tokens, text, para in all_para_tokens:
            if not text.strip() or not tokens:
                continue

            # 处理长句：如果token数量超过阈值，则分行显示
            max_tokens_per_line = 10  # 每行最大token数量

            if len(tokens) > max_tokens_per_line:
                # 分割长句为多行
                token_chunks = []
                for i in range(0, len(tokens), max_tokens_per_line):
                    chunk = tokens[i:i + max_tokens_per_line]
                    token_chunks.append(chunk)

                # 为每个chunk创建单独的表格
                for chunk_tokens in token_chunks:
                    colwidths = self.get_col_widths(chunk_tokens)
                    table = doc_out.add_table(rows=2, cols=len(chunk_tokens))
                    set_table_noborder_and_tight(table, colwidths)
                    table.allow_autofit = False

                    # 处理当前chunk的tokens
                    self._process_table_tokens(table, chunk_tokens, text, para, wordset, meaning_cache, worddict, ch_font_name, ch_font_size)

                    # 在表格之间添加小间距
                    p = doc_out.add_paragraph()
                    if 'TinySpacing' in doc_out.styles:
                        p.style = doc_out.styles['TinySpacing']
            else:
                # 正常处理短句
                colwidths = self.get_col_widths(tokens)
                table = doc_out.add_table(rows=2, cols=len(tokens))
                set_table_noborder_and_tight(table, colwidths)
                table.allow_autofit = False

                # 处理tokens
                self._process_table_tokens(table, tokens, text, para, wordset, meaning_cache, worddict, ch_font_name, ch_font_size)

                # 添加段落间距
                p = doc_out.add_paragraph()
                if 'TinySpacing' in doc_out.styles:
                    p.style = doc_out.styles['TinySpacing']

        output_path = os.path.abspath("output.docx")
        doc_out.save(output_path)
        set_progress("文档已完成。", 100)
        return output_path

    def _process_table_tokens(self, table, tokens, text, para, wordset, meaning_cache, worddict, ch_font_name, ch_font_size):
        """处理表格中的tokens，包括英文单词和中文释义"""

        # 处理英文单词行（第一行）
        ptr = 0
        for j, word in enumerate(tokens):
            cell = table.cell(0, j)
            cell.text = ''

            # 改进的字符定位逻辑，更准确地处理上标下标
            font_name = None
            font_size = None
            is_superscript = False
            is_subscript = False

            # 使用更精确的字符定位方法
            word_start = self.find_word_position_in_text(word, text, ptr)

            if word_start != -1 and para.runs:
                # 获取该位置的格式信息
                format_info = self.get_format_at_position(para, word_start, word)
                font_name = format_info['font_name']
                font_size = format_info['font_size']
                is_superscript = format_info['is_superscript']
                is_subscript = format_info['is_subscript']

            if word_start != -1:
                ptr = word_start + len(word)

            # 设置默认值
            if not font_name:
                font_name = "Georgia"
            if not font_size:
                # 如果无法从原文档获取字体大小，使用合理的默认值
                font_size = 12  # 使用12号作为默认英文字体大小

            # 智能处理包含上下标字符的单词
            self._add_formatted_word_to_cell(cell, word, font_name, font_size, is_superscript, is_subscript, wordset)
            pf2 = cell.paragraphs[0].paragraph_format
            pf2.space_before = Pt(0)
            pf2.space_after = Pt(0)
            pf2.line_spacing = 1.0
            cell.paragraphs[0].alignment = 1

        # 处理中文释义行（第二行）
        ptr = 0
        for j, word in enumerate(tokens):
            cell = table.cell(1, j)
            cell.text = ''
            tkey = strip_punct(word)
            curidx = text.lower().find(tkey, ptr) if tkey else -1
            ptr = curidx + len(tkey) if curidx != -1 else ptr
            sent = split_sentence_for_context(text, curidx).strip() if curidx != -1 else ""
            if tkey in wordset:
                meaning = meaning_cache.get((tkey, sent), "")
                logging.info(f"Word: '{tkey}', Sentence: '{sent}', Fetched AI-meaning: '{meaning}'")
                if meaning.strip():
                    m2 = split_meaning_two_lines(meaning, max_len=8)
                else:
                    local_meaning = worddict.get(tkey, "（无释义）")
                    logging.info(f"AI meaning is empty, using local dictionary for '{tkey}': '{local_meaning}'")
                    m2 = split_meaning_two_lines(local_meaning, max_len=8)
                run = cell.paragraphs[0].add_run(m2)
                # 使用新的字体设置函数，确保楷体等中文字体正确应用
                self.set_chinese_font(run, ch_font_name, ch_font_size)
            pf2 = cell.paragraphs[0].paragraph_format
            pf2.space_before = Pt(0)
            pf2.space_after = Pt(0)
            pf2.line_spacing = 1.0
            cell.paragraphs[0].alignment = 1

        # 设置表格行保持在一起
        for cell in table.rows[0].cells:
            for paragraph in cell.paragraphs:
                paragraph.paragraph_format.keep_with_next = True

if __name__ == "__main__":
    root = tk.Tk()
    app = WordAssistantApp(root)
    root.mainloop()
