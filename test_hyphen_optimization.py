#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连字符单词优化效果的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from docx import Document
from docx.shared import Pt, Cm
from oringin import WordAssistantApp
import tkinter as tk

def test_hyphen_word_widths():
    """测试连字符单词的宽度计算"""

    # 创建一个临时的应用实例来测试宽度计算
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    app = WordAssistantApp(root)

    # 测试用例：包含不同长度连字符单词的token列表
    test_cases = [
        # 测试用例1：包含double-action的句子（问题单词）
        ["The", "revolver's", "double-action", "trigger", "allows", "both", "cocking", "and", "firing"],

        # 测试用例2：普通句子（对照组）
        ["The", "power", "outage", "lasted", "hours"],

        # 测试用例3：包含一个连字符单词的句子
        ["This", "user-friendly", "device", "works", "well"],

        # 测试用例4：包含短连字符单词
        ["Real-time", "processing", "is", "important"],

        # 测试用例5：全普通单词句子（对照组）
        ["Calculate", "using", "the", "power", "rule"]
    ]
    
    print("=== 连字符单词宽度优化测试 ===\n")
    
    for i, tokens in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {' '.join(tokens)}")
        print("-" * 50)
        
        # 计算列宽
        colwidths = app.get_col_widths(tokens)
        
        # 显示每个单词的宽度
        for j, (token, width) in enumerate(zip(tokens, colwidths)):
            width_cm = width.cm
            is_hyphen = '-' in token
            status = "【连字符】" if is_hyphen else "【普通词】"
            print(f"  {j+1:2d}. {token:15s} {status} 宽度: {width_cm:.2f}cm")
        
        # 统计信息
        hyphen_words = [token for token in tokens if '-' in token]
        normal_words = [token for token in tokens if '-' not in token]
        hyphen_widths = [width.cm for token, width in zip(tokens, colwidths) if '-' in token]
        normal_widths = [width.cm for token, width in zip(tokens, colwidths) if '-' not in token]
        
        print(f"\n  统计信息:")
        print(f"    连字符单词数量: {len(hyphen_words)}")
        print(f"    普通单词数量: {len(normal_words)}")
        if hyphen_widths:
            print(f"    连字符单词平均宽度: {sum(hyphen_widths)/len(hyphen_widths):.2f}cm")
            print(f"    连字符单词最大宽度: {max(hyphen_widths):.2f}cm")
        if normal_widths:
            print(f"    普通单词平均宽度: {sum(normal_widths)/len(normal_widths):.2f}cm")
        
        print("\n" + "="*60 + "\n")
    
    root.destroy()

def create_test_document():
    """创建一个测试文档来验证实际效果"""
    
    print("创建测试文档...")
    
    # 创建测试文档
    doc = Document()
    
    # 添加标题
    title = doc.add_heading('连字符单词优化测试文档', 0)
    
    # 添加说明段落
    doc.add_paragraph('本文档用于测试连字符单词在表格中的显示效果。')
    
    # 测试句子
    test_sentences = [
        "The revolver's double-action trigger allows both cocking and firing in one motion.",
        "This state-of-the-art user-friendly device provides real-time processing capabilities.",
        "The self-contained multi-purpose system ensures long-term stability and high-quality performance.",
        "Co-operation between well-known manufacturers enables short-term improvements.",
        "Single-use components require careful handling during assembly processes."
    ]
    
    for sentence in test_sentences:
        doc.add_paragraph(sentence)
    
    # 保存测试文档
    test_doc_path = "test_hyphen_document.docx"
    doc.save(test_doc_path)
    print(f"测试文档已保存为: {test_doc_path}")
    
    return test_doc_path

def main():
    """主函数"""
    print("开始连字符单词优化测试...\n")
    
    # 测试宽度计算
    test_hyphen_word_widths()
    
    # 创建测试文档
    test_doc_path = create_test_document()
    
    print("\n=== 测试完成 ===")
    print("优化要点总结:")
    print("1. 连字符单词获得了更大的基础宽度和字符宽度")
    print("2. 根据单词长度动态调整额外宽度")
    print("3. 常见连字符单词获得特别优化")
    print("4. 设置了动态最小宽度确保不换行")
    print("5. 放宽了最大宽度限制")
    print("6. 在表格设置中添加了防换行控制")
    print(f"\n请使用生成的测试文档 '{test_doc_path}' 来验证实际效果。")

if __name__ == "__main__":
    main()
