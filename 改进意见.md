原设计师python代码的运行情况说明：

1.  运行python代码：选择word文档A、单词表B、输入阿里云API-key、点击"处理文档A"后，生成中间临时文档"processed_output.docx"；（目的是对word文档A以"."为标志
    ，进行分段；而分段的目的是减少每行的英文长度，以便能够尽可能容纳中文翻译）

2.  再次输入python代码：选择word文档"processed_output.docx"（即上次处理生成的中间临时文档），选择单词表B、输入阿里云API-key、点击"开始处理"，生成最终word文档"output.docx"。

个人对原设计师python代码的改进优化意见：

3.  如图所示，最终生成的文档"output.docx"存在大量的空行（据原设计师说是表格间的空段），现在需要程序运行时就要批量删除这些空段！

![](media/image1.png){width="5.768055555555556in"
height="3.5131944444444443in"}

4.  如图所示，程序运行时，比如选择中文字体为楷体（或其他字体），最终生成的文档"output.docx"中的中文并不是楷体（或其他字体），请求处理。

![](media/image2.png){width="5.768055555555556in"
height="3.4604166666666667in"}

5.  如图所示，最终生成的文档"output.docx"中，\$与150，90与%（或其他类似情况），中间有空格；但原文是没有空格的哦，请求处理！

![](media/image3.jpeg){width="5.768055555555556in"
height="3.8895833333333334in"}
