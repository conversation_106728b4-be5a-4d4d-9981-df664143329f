#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体设置测试脚本
用于验证楷体字体设置是否正确工作
"""

from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn
import os

def test_font_setting():
    """测试不同字体设置方法"""
    
    # 创建新文档
    doc = Document()
    
    # 测试1：基本字体设置（可能不生效）
    p1 = doc.add_paragraph()
    run1 = p1.add_run("测试1：基本字体设置 - 楷体")
    run1.font.name = "KaiTi"
    run1.font.size = Pt(14)
    
    # 测试2：完整字体设置（推荐方法）
    p2 = doc.add_paragraph()
    run2 = p2.add_run("测试2：完整字体设置 - 楷体")
    run2.font.name = "KaiTi"
    run2.font.size = Pt(14)
    
    # 设置完整的字体族属性
    r = run2._r
    rFonts = r.rPr.rFonts
    rFonts.set(qn('w:ascii'), "KaiTi")
    rFonts.set(qn('w:hAnsi'), "KaiTi")
    rFonts.set(qn('w:eastAsia'), "KaiTi")
    rFonts.set(qn('w:cs'), "KaiTi")
    
    # 测试3：使用中文字体名称
    p3 = doc.add_paragraph()
    run3 = p3.add_run("测试3：中文字体名称 - 楷体")
    run3.font.name = "楷体"
    run3.font.size = Pt(14)
    
    r3 = run3._r
    rFonts3 = r3.rPr.rFonts
    rFonts3.set(qn('w:ascii'), "楷体")
    rFonts3.set(qn('w:hAnsi'), "楷体")
    rFonts3.set(qn('w:eastAsia'), "楷体")
    rFonts3.set(qn('w:cs'), "楷体")
    
    # 测试4：多种字体名称兼容性测试
    font_variants = ["KaiTi", "楷体", "STKaiti", "Kai"]
    for i, font_name in enumerate(font_variants, 4):
        p = doc.add_paragraph()
        run = p.add_run(f"测试{i}：{font_name} - 楷体字体测试")
        run.font.name = font_name
        run.font.size = Pt(14)
        
        r = run._r
        rFonts = r.rPr.rFonts
        rFonts.set(qn('w:ascii'), font_name)
        rFonts.set(qn('w:hAnsi'), font_name)
        rFonts.set(qn('w:eastAsia'), font_name)
        rFonts.set(qn('w:cs'), font_name)
    
    # 对比测试：宋体（应该正常显示）
    p_compare = doc.add_paragraph()
    run_compare = p_compare.add_run("对比测试：宋体字体（正常显示）")
    run_compare.font.name = "SimSun"
    run_compare.font.size = Pt(14)
    
    r_compare = run_compare._r
    rFonts_compare = r_compare.rPr.rFonts
    rFonts_compare.set(qn('w:ascii'), "SimSun")
    rFonts_compare.set(qn('w:hAnsi'), "SimSun")
    rFonts_compare.set(qn('w:eastAsia'), "SimSun")
    rFonts_compare.set(qn('w:cs'), "SimSun")
    
    # 保存测试文档
    output_path = os.path.abspath("font_test.docx")
    doc.save(output_path)
    
    print(f"字体测试文档已生成：{output_path}")
    print("\n请打开文档检查以下内容：")
    print("1. 测试1-7中哪种方法能正确显示楷体")
    print("2. 对比宋体字体的显示效果")
    print("3. 确认楷体字体是否与宋体有明显区别")
    
    return output_path

if __name__ == "__main__":
    test_font_setting()
