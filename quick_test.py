#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试连字符单词宽度的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import WordAssistantApp
import tkinter as tk

def quick_test():
    """快速测试各种连字符单词的宽度"""
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    
    # 测试各种连字符单词
    hyphen_words = [
        "double-action",
        "state-of-the-art", 
        "user-friendly",
        "well-known",
        "real-time",
        "long-term",
        "short-term",
        "high-quality",
        "self-contained",
        "multi-purpose",
        "co-operation",
        "single-use"
    ]
    
    print("=== 连字符单词宽度快速测试 ===\n")
    
    for word in hyphen_words:
        # 创建包含该单词的简单句子
        tokens = ["The", word, "system", "works", "well"]
        colwidths = app.get_col_widths(tokens)
        
        # 找到连字符单词的宽度
        word_width = colwidths[1].cm  # 第二个位置是连字符单词
        
        print(f"{word:18s} -> {word_width:.2f}cm")
    
    print(f"\n普通单词对照:")
    normal_tokens = ["The", "normal", "system", "works", "well"]
    normal_widths = app.get_col_widths(normal_tokens)
    for i, token in enumerate(normal_tokens):
        print(f"{token:18s} -> {normal_widths[i].cm:.2f}cm")
    
    root.destroy()

if __name__ == "__main__":
    quick_test()
